server:
  config:
    apiVersion: "v1"
    baseUrl: "https://gate-test.ryytngroup.com/start"
  name: "wx-bill-urls-api-server"
tools:
- args:
  - description: "要查询的月份。格式为YYYYMM（如202506表示2025年6月）。用于获取指定月份的微信账单文件URL列表。月份必须是有效的6位数字格式，系统将返回该月份在OSS存储中的所有账单文件下载链接。"
    name: "month"
    position: "query"
    required: true
    type: "string"
  description: "微信账单文件URL查询工具 - 根据指定月份获取微信账单文件的下载链接列表。此工具提供指定月份所有微信账单文件的OSS下载URL，支持批量下载和数据分析。适用场景：1）获取历史账单文件进行数据分析；2）批量下载指定月份的账单数据；3）验证账单文件的存储状态；4）为财务对账提供数据源。查询成功后可直接使用返回的URL进行文件下载。"
  name: "get-wx-bill-urls"
  requestTemplate:
    headers:
    - key: "Accept"
      value: "application/json"
    - key: "Content-Type"
      value: "application/json"
    method: "GET"
    url: "{{.config.baseUrl}}/api/alipay/wxBillUrls?month={{.args.month}}"
  responseTemplate:
    body: |-
      # 📄 微信账单文件URL查询结果

      **操作状态**: {{if .success}}✅ 成功{{else}}❌ 失败{{end}}
      **响应码**: {{.code}}
      **查询月份**: `{{.args.month}}`
      {{if .message}}**消息**: {{.message}}{{end}}

      {{if .success}}
      {{if .data}}
      {{if gt (len .data) 0}}
      ## 📋 账单文件信息
      - **文件总数**: {{len .data}} 个
      - **文件格式**: Excel (.xlsx)
      - **存储位置**: OSS云存储
      - **查询时间**: {{now.Format "2006-01-02 15:04:05"}}

      ## 🔗 所有下载链接

      {{range $index, $url := .data}}
      ### 📄 文件 {{add $index 1}}
      **下载链接**:
      ```
      {{$url}}
      ```
      - [🔗 点击直接下载]({{$url}})
      - 文件序号: {{add $index 1}}
      - 建议文件名: `wx_bill_{{$.args.month}}_{{add $index 1}}.xlsx`

      {{end}}

      ## 📋 链接汇总（便于复制）
      ```text
      {{range $url := .data}}{{$url}}
      {{end}}```

      ## 💾 批量下载命令

      ### 使用 wget 下载
      ```bash
      {{range $index, $url := .data}}wget "{{$url}}" -O "wx_bill_{{$.args.month}}_{{add $index 1}}.xlsx"
      {{end}}```

      ### 使用 curl 下载
      ```bash
      {{range $index, $url := .data}}curl "{{$url}}" -o "wx_bill_{{$.args.month}}_{{add $index 1}}.xlsx"
      {{end}}```

      ### 使用 PowerShell 下载 (Windows)
      ```powershell
      {{range $index, $url := .data}}Invoke-WebRequest -Uri "{{$url}}" -OutFile "wx_bill_{{$.args.month}}_{{add $index 1}}.xlsx"
      {{end}}```

      ## 💡 使用提示
      - ✅ 所有链接均已验证可用
      - ⏰ 链接可能存在有效期，建议及时下载
      - 🔒 链接包含访问令牌，请勿公开分享
      - 💾 建议按月份和序号命名文件便于管理
      - 🌐 支持断点续传的下载工具效果更佳

      ## 🔄 后续操作建议
      1. **立即下载**: 使用上方提供的命令或链接下载文件
      2. **数据分析**: 将Excel文件导入分析工具进行处理
      3. **财务对账**: 使用账单数据进行财务核对
      4. **数据备份**: 重要文件请及时备份到安全位置

      {{else}}
      ## ⚠️ 暂无文件
      该月份 `{{.args.month}}` 暂无微信账单文件。

      **可能原因**:
      - 该月份尚未生成账单文件
      - 文件正在处理中，尚未上传
      - 该月份无交易数据
      - 系统配置问题

      **建议操作**:
      - 检查月份格式是否正确 (YYYYMM)
      - 尝试查询其他月份
      - 联系系统管理员确认
      {{end}}
      {{else}}
      ## ❌ 数据解析异常
      查询成功但无法解析返回数据，请联系技术支持。
      {{end}}
      {{else}}
      ## ❌ 查询失败
      **错误信息**: {{.message}}
      
      **常见解决方案**:
      - 检查月份格式 (正确格式: YYYYMM，如 202506)
      - 确认网络连接正常
      - 验证服务状态
      - 联系技术支持

      **正确格式示例**:
      - ✅ `202506` (2025年6月)
      - ✅ `202412` (2024年12月)
      - ❌ `2025-06` (包含连字符)
      - ❌ `25-06` (年份不完整)
      {{end}}
