# 微信账单API工具使用指南

## 概述

本文档介绍了 `/api/alipay/wxBillUrls` 接口的YAML配置工具，该工具用于获取指定月份的微信账单文件下载链接。

## 配置文件说明

### 文件位置
- **配置文件**: `api-tools-config.yml`
- **接口路径**: `/api/alipay/wxBillUrls`
- **请求方法**: GET

### 工具列表

#### 1. get-wx-bill-urls
**功能**: 获取指定月份的微信账单文件URL列表

**参数**:
- `month` (必需): 月份，格式为YYYYMM（如202506）

**使用示例**:
```yaml
month: "202506"  # 查询2025年6月的账单文件
```

#### 2. check-wx-bill-exists
**功能**: 快速检查指定月份是否存在微信账单文件

**参数**:
- `month` (必需): 月份，格式为YYYYMM

## 响应格式说明

### 成功响应
当查询成功且存在文件时，响应将包含：

1. **文件列表详情**
   - 查询月份
   - 文件总数
   - 文件状态

2. **下载链接列表**
   - 每个文件的详细信息
   - 直接下载链接
   - 文件URL（可复制）

3. **所有链接汇总**
   - 纯文本格式的所有链接
   - 便于批量复制使用

4. **批量下载命令**
   - wget命令示例
   - curl命令示例
   - 自动生成文件名

### 错误处理
配置包含详细的错误处理机制：

- **参数错误**: 月份格式不正确
- **业务错误**: OSS服务异常或文件不存在
- **系统错误**: 内部服务异常
- **未知错误**: 其他异常情况

## 使用场景

### 1. 数据分析师
```bash
# 获取最近3个月的账单数据
curl "https://gate-test.ryytngroup.com/start/api/alipay/wxBillUrls?month=202506"
curl "https://gate-test.ryytngroup.com/start/api/alipay/wxBillUrls?month=202505"
curl "https://gate-test.ryytngroup.com/start/api/alipay/wxBillUrls?month=202504"
```

### 2. 财务人员
```bash
# 下载指定月份的所有账单文件
wget "返回的URL1" -O "wx_bill_202506_1.xlsx"
wget "返回的URL2" -O "wx_bill_202506_2.xlsx"
```

### 3. 系统监控
```bash
# 检查文件存在性
curl "https://gate-test.ryytngroup.com/start/api/alipay/wxBillUrls?month=202506" | grep "文件总数"
```

## 最佳实践

### 1. 参数格式
- ✅ 正确: `202506` (2025年6月)
- ✅ 正确: `202412` (2024年12月)
- ❌ 错误: `2025-06` (包含连字符)
- ❌ 错误: `25-06` (年份不完整)

### 2. 批量下载
```bash
#!/bin/bash
# 批量下载脚本示例
MONTH="202506"
API_URL="https://gate-test.ryytngroup.com/start/api/alipay/wxBillUrls?month=${MONTH}"

# 获取URL列表并下载
curl -s "${API_URL}" | grep -o 'https://[^"]*\.xlsx[^"]*' | while read url; do
    filename="wx_bill_${MONTH}_$(date +%s).xlsx"
    wget "$url" -O "$filename"
    echo "Downloaded: $filename"
done
```

### 3. 错误处理
```bash
#!/bin/bash
response=$(curl -s "https://gate-test.ryytngroup.com/start/api/alipay/wxBillUrls?month=202506")
if echo "$response" | grep -q "✅ 成功"; then
    echo "查询成功"
    # 处理成功响应
else
    echo "查询失败"
    # 处理错误响应
fi
```

## 安全注意事项

1. **链接保护**: 返回的OSS链接可能包含访问令牌，请勿公开分享
2. **数据安全**: 下载的文件包含敏感财务数据，请妥善保管
3. **访问控制**: 确保只有授权人员可以访问这些接口
4. **日志记录**: 建议记录所有下载操作，便于审计

## 故障排除

### 常见问题

1. **参数格式错误**
   - 检查月份格式是否为YYYYMM
   - 确认月份数值在有效范围内

2. **文件不存在**
   - 确认该月份是否应该有数据
   - 检查账单生成流程是否正常

3. **网络连接问题**
   - 检查网络连接状态
   - 确认服务器地址是否正确

4. **权限问题**
   - 确认API访问权限
   - 检查OSS存储访问权限

### 联系支持
如遇到无法解决的问题，请联系技术支持并提供：
- 接口调用参数
- 完整的错误信息
- 操作时间
- 网络环境信息

## 更新日志

- **v1.0**: 初始版本，支持基本的文件URL查询功能
- **v1.1**: 增加文件存在性检查工具
- **v1.2**: 优化响应格式，增加批量下载命令生成
- **v1.3**: 增强错误处理和安全提示

---

*最后更新: 2025-01-31*
