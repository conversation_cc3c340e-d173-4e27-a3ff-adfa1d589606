server:
  config:
    apiVersion: "v1"
    baseUrl: "https://gate-test.ryytngroup.com/start"
  name: "spring-boot-test-api-server"
tools:
- args:
  - description: "要查询的月份。格式为YYYYMM（如202506表示2025年6月）。用于获取指定月份的微信账单文件URL列表。月份必须是有效的6位数字格式，系统将返回该月份在OSS存储中的所有账单文件下载链接。"
    name: "month"
    position: "query"
    required: true
    type: "string"
  description: "微信账单文件URL查询工具 - 根据指定月份获取微信账单文件的下载链接列表。此工具提供指定月份所有微信账单文件的OSS下载URL，支持批量下载和数据分析。适用场景：1）获取历史账单文件进行数据分析；2）批量下载指定月份的账单数据；3）验证账单文件的存储状态；4）为财务对账提供数据源。查询成功后可直接使用返回的URL进行文件下载。"
  name: "get-wx-bill-urls"
  requestTemplate:
    headers:
    - key: "Accept"
      value: "application/json"
    - key: "Content-Type"
      value: "application/json"
    method: "GET"
    url: "{{.config.baseUrl}}/api/alipay/wxBillUrls?month={{.args.month}}"
  responseTemplate:
    body: |-
      # 📄 微信账单文件URL查询结果

      **操作状态**: {{if .success}}✅ 成功{{else}}❌ 失败{{end}}
      **响应码**: {{.code}}
      {{if .message}}**消息**: {{.message}}{{end}}
      {{if .subCode}}**业务码**: {{.subCode}}{{end}}

      ## 📋 账单文件信息
      {{if .success}}
      {{if .data}}
      ### 📁 文件列表详情
      - **查询月份**: `{{.args.month}}`
      - **文件总数**: {{len .data}} 个
      - **文件状态**: {{if gt (len .data) 0}}✅ 存在账单文件{{else}}⚠️ 暂无账单文件{{end}}

      {{if gt (len .data) 0}}
      ### 🔗 下载链接列表
      {{range $index, $url := .data}}
      **文件 {{add $index 1}}**:
      - **下载链接**: [点击下载]({{$url}})
      - **文件URL**: `{{$url}}`
      {{end}}

      ### 📊 文件统计信息
      - **可用文件数**: {{len .data}} 个
      - **文件格式**: Excel (.xlsx)
      - **存储位置**: OSS云存储
      - **访问方式**: 直接URL下载

      ### 🔄 建议的后续操作
      ✨ **查询成功！** 根据文件信息，您现在可以：

      1. **批量下载** - 使用提供的URL链接批量下载所有账单文件
      2. **数据分析** - 将下载的Excel文件导入数据分析工具进行处理
      3. **财务对账** - 使用账单数据进行财务核对和对账操作
      4. **存档备份** - 将重要的账单文件进行本地存档备份
      5. **数据验证** - 检查账单数据的完整性和准确性

      ### 💡 使用提示
      - **文件有效期**: URL链接可能存在有效期限制，建议及时下载
      - **网络稳定**: 下载大文件时请确保网络连接稳定
      - **存储空间**: 请确保本地有足够的存储空间
      - **数据安全**: 下载后请妥善保管账单数据，注意数据安全

      💡 **推荐下一步**: 根据业务需要选择合适的文件进行下载和处理
      {{else}}
      ### ⚠️ 暂无文件
      - **查询月份**: `{{.args.month}}`
      - **文件状态**: 该月份暂无微信账单文件
      - **可能原因**:
        - 该月份尚未生成账单文件
        - 文件正在处理中，尚未上传到OSS
        - 该月份无交易数据
        - 文件路径配置问题

      ### 🔍 排查建议
      1. **确认月份** - 检查输入的月份格式是否正确（YYYYMM）
      2. **检查时间** - 确认该月份是否应该有账单数据
      3. **联系管理员** - 如确认应该有数据，请联系系统管理员
      4. **尝试其他月份** - 可以尝试查询其他已知有数据的月份

      ### 🚀 建议操作
      1. **验证参数** - 确认月份参数格式正确
      2. **查询其他月份** - 尝试查询最近几个月的数据
      3. **系统检查** - 检查账单生成和上传流程是否正常

      💡 **建议**: 尝试查询最近的月份，如 202412、202501 等

      ### 🔧 常见月份格式
      - **当前月份**: {{now.Format "200601"}}
      - **上个月**: {{now.AddDate 0 -1 0 | date "200601"}}
      - **去年同月**: {{now.AddDate -1 0 0 | date "200601"}}
      {{end}}
      {{else}}
      ⚠️ **查询成功但无数据** - 可能存在数据解析问题

      ### 🔍 排查建议
      1. **检查数据格式** - 确认返回数据的结构是否正确
      2. **验证序列化** - 检查实体序列化配置
      3. **日志分析** - 查看后端日志了解详细情况
      4. **接口测试** - 使用其他工具测试接口返回

      💡 **建议**: 联系技术支持进行进一步排查
      {{end}}
      {{else}}
      ## ❌ 查询失败详情

      {{if eq .subCode "PARAMS_ERROR"}}
      ### 📝 参数错误
      - **错误类型**: 参数验证失败
      - **可能原因**:
        - 月份参数为空或格式不正确
        - 月份格式不是YYYYMM格式
        - 月份数值超出有效范围
      - **解决方案**:
        - 确认月份格式为YYYYMM（如202506）
        - 检查月份是否为有效的日期
        - 确保参数传递正确

      ### 📋 正确格式示例
      - ✅ **正确**: `202506` (2025年6月)
      - ✅ **正确**: `202412` (2024年12月)
      - ❌ **错误**: `2025-06` (包含连字符)
      - ❌ **错误**: `25-06` (年份不完整)
      - ❌ **错误**: `202513` (月份超出范围)

      💡 **建议**: 使用正确的YYYYMM格式重新查询
      {{else if eq .subCode "BUSINESS_ERROR"}}
      ### 🔍 业务逻辑错误
      - **错误类型**: 业务处理异常
      - **可能原因**:
        - OSS存储服务连接异常
        - 指定月份的目录不存在
        - 文件访问权限问题
        - 存储路径配置错误
      - **解决方案**:
        - 检查OSS服务状态
        - 确认存储路径配置
        - 验证访问权限设置

      ### 🚀 建议操作
      1. **服务检查** - 检查OSS存储服务是否正常
      2. **权限验证** - 确认系统对OSS的访问权限
      3. **路径确认** - 验证文件存储路径配置
      4. **重试操作** - 稍后重试查询操作

      💡 **推荐下一步**: 联系系统管理员检查OSS服务状态
      {{else if eq .subCode "SYSTEM_ERROR"}}
      ### 🚨 系统错误
      - **错误类型**: 系统内部异常
      - **可能原因**:
        - OSS服务连接超时
        - 系统内部服务异常
        - 网络连接问题
        - 服务器资源不足
      - **解决方案**:
        - 检查系统状态
        - 稍后重试
        - 联系技术支持

      ### ⚠️ 注意事项
      - 🚫 **请勿频繁重试** - 系统错误通常需要人工干预解决
      - 📞 **及时联系** - 建议立即联系技术支持团队
      - 📋 **保留信息** - 记录错误发生的时间和具体操作

      💡 **建议**: 检查系统状态或联系技术支持
      {{else}}
      ### ❓ 未知错误
      - **错误信息**: {{.message}}
      - **错误码**: {{.subCode}}
      - **查询月份**: {{.args.month}}
      - **建议**: 联系技术支持并提供完整的错误信息

      ### 📋 错误报告信息
      请向技术支持提供以下信息：
      - **接口**: `/api/alipay/wxBillUrls`
      - **参数**: month={{.args.month}}
      - **错误码**: {{.subCode}}
      - **错误消息**: {{.message}}
      - **发生时间**: 当前时间

      🚫 **避免重试** - 未知错误可能需要专业技术人员处理
      {{end}}

      ### 🔄 替代方案
      如果当前查询无法成功，您可以：
      1. **参数检查** - 仔细检查月份参数的格式和有效性
      2. **网络测试** - 检查网络连接是否正常
      3. **服务状态** - 确认相关服务是否正常运行
      4. **联系支持** - 如问题持续存在，请联系技术支持

      💡 **推荐**: 首先检查参数格式，然后联系技术支持
      {{end}}

      {{if .total}}**总计**: {{.total}}{{end}}

- args:
  - description: "要查询的月份。格式为YYYYMM（如202506表示2025年6月）。用于验证指定月份是否存在微信账单文件。此参数用于快速检查文件存在性，无需获取完整的URL列表。"
    name: "month"
    position: "query"
    required: true
    type: "string"
  description: "微信账单文件存在性检查工具 - 快速检查指定月份是否存在微信账单文件。此工具提供轻量级的文件存在性验证，适用于批量检查多个月份的数据可用性。适用场景：1）批量验证历史数据的完整性；2）在下载前预检查文件是否存在；3）系统健康检查和监控；4）数据完整性审计。返回简单的存在性状态信息。"
  name: "check-wx-bill-exists"
  requestTemplate:
    headers:
    - key: "Accept"
      value: "application/json"
    method: "GET"
    url: "{{.config.baseUrl}}/api/alipay/wxBillUrls?month={{.args.month}}"
  responseTemplate:
    body: |-
      # 🔍 微信账单文件存在性检查结果

      **操作状态**: {{if .success}}✅ 成功{{else}}❌ 失败{{end}}
      **响应码**: {{.code}}
      **查询月份**: `{{.args.month}}`

      ## 📋 检查结果
      {{if .success}}
      {{if .data}}
      ### ✅ 文件存在确认
      - **文件状态**: {{if gt (len .data) 0}}✅ 存在 ({{len .data}} 个文件){{else}}❌ 不存在{{end}}
      - **检查月份**: `{{.args.month}}`
      - **检查时间**: {{now.Format "2006-01-02 15:04:05"}}

      {{if gt (len .data) 0}}
      ### 📊 基本统计
      - **文件数量**: {{len .data}} 个
      - **数据状态**: ✅ 可用
      - **建议操作**: 可以使用 `get-wx-bill-urls` 工具获取详细的下载链接

      💡 **下一步**: 使用 `get-wx-bill-urls` 工具获取完整的文件列表和下载链接
      {{else}}
      ### ❌ 文件不存在
      - **检查结果**: 该月份暂无微信账单文件
      - **建议**: 检查其他月份或联系管理员确认数据状态
      {{end}}
      {{else}}
      ### ⚠️ 检查异常
      - **状态**: 无法获取文件信息
      - **建议**: 联系技术支持进行排查
      {{end}}
      {{else}}
      ### ❌ 检查失败
      - **错误信息**: {{.message}}
      - **建议**: 检查参数格式或联系技术支持
      {{end}}
